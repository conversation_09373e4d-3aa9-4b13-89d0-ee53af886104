<?php
session_start();

// Database connection
require_once "database.php";

// Update logout time if user was logged in
if (isset($_SESSION["user_id"])) {
    $user_id = $_SESSION["user_id"];
    
    // Get the most recent login record for this user
    $query = "UPDATE login_logs 
              SET logout_time = NOW() 
              WHERE user_id = ? 
              AND logout_time IS NULL 
              ORDER BY login_time DESC 
              LIMIT 1";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
}

// Destroy the session
session_unset();
session_destroy();

// Redirect to login page
header("Location: login.php");
exit();
?>
