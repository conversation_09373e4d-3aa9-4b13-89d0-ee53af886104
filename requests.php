<?php
session_start();

// Check if the user is logged in and is an admin
if (isset($_SESSION["user"]) && $_SESSION["role"] === "admin") {
    // Admin is logged in
} else {
    // Redirect unauthorized users to another page
    header("Location: index.php");
    exit();
}

// Database connection
require_once "database.php";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["approve"])) {
    // Reset all requested passwords to default
    $updateSql = "UPDATE users SET password = ?";
    $stmt = mysqli_stmt_init($conn);

    if (!mysqli_stmt_prepare($stmt, $updateSql)) {
        die("SQL error");
    } else {
        $defaultPassword = password_hash("123456789", PASSWORD_DEFAULT);
        mysqli_stmt_bind_param($stmt, "s", $defaultPassword);
        mysqli_stmt_execute($stmt);

        // Clear all password reset requests after approving
        $clearRequestsSql = "DELETE FROM password_reset_requests";
        mysqli_query($conn, $clearRequestsSql);

        echo "<div class='alert alert-success'>All passwords reset to default successfully.</div>";

        // Redirect to index.php
        header("Location: index.php");
        exit();
    }
}

// Display reset requests and approve button
$requestsSql = "SELECT * FROM password_reset_requests";
$requestsResult = mysqli_query($conn, $requestsSql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Include your CSS and other header content -->
    <?php include('CSS/header.php'); ?>
</head>
<body>
<?php include ('CSS/sidebar.php'); ?>
<div>
    <h1>Password Reset Requests</h1>

    <?php
    if (mysqli_num_rows($requestsResult) > 0) {
        echo "<form action='requests.php' method='post'>";
        while ($row = mysqli_fetch_assoc($requestsResult)) {
            echo "<p>{$row['full_name']} - {$row['email']}</p>";
        }
        echo "<input type='submit' name='approve' value='Approve Reset'>";
        echo "</form>";
    } else {
        echo "<p>No password reset requests.</p>";
    }
    ?>
</div>

<?php include('CSS/footer.php'); ?>

</body>
</html>
