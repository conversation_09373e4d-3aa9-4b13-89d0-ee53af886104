<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Camera Selection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        #qr-reader {
            width: 100%;
            max-width: 400px;
            height: 400px;
            border: 2px solid #ddd;
            margin: 20px auto;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            background: #175883;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .camera-info {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Auto Camera Selection Test</h1>
    
    <div class="controls">
        <button id="start-btn">Start Auto Camera</button>
        <button id="stop-btn" disabled>Stop Camera</button>
    </div>
    
    <div id="qr-reader"></div>
    
    <div id="result" class="result"></div>
    
    <div id="camera-info" class="camera-info">
        <strong>Camera Information:</strong><br>
        Loading...
    </div>

    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <script>
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const result = document.getElementById('result');
        const cameraInfo = document.getElementById('camera-info');
        
        let html5QrCode;
        let isScanning = false;
        let selectedCameraId = null;
        
        function log(message) {
            console.log(message);
            cameraInfo.innerHTML += '<br>' + message;
        }
        
        function showResult(message, type) {
            result.className = `result ${type}`;
            result.innerHTML = message;
            result.style.display = 'block';
            log(`Result: ${message}`);
        }
        
        function selectBestCamera(devices) {
            log(`Found ${devices.length} cameras:`);
            devices.forEach((device, index) => {
                log(`  ${index + 1}. ${device.label || 'Unknown'} (${device.id})`);
            });
            
            // Prefer back camera (environment) for mobile devices
            const backCamera = devices.find(device => 
                device.label.toLowerCase().includes('back') || 
                device.label.toLowerCase().includes('environment') ||
                device.label.toLowerCase().includes('rear')
            );
            
            if (backCamera) {
                log(`✓ Selected back camera: ${backCamera.label}`);
                return backCamera.id;
            }
            
            // Otherwise, use the first available camera
            log(`✓ Selected first available camera: ${devices[0].label || 'Unknown'}`);
            return devices[0].id;
        }
        
        // Initialize
        try {
            html5QrCode = new Html5Qrcode("qr-reader");
            log('QR scanner initialized successfully');
        } catch (error) {
            log('Error initializing QR scanner: ' + error);
            showResult('Error initializing scanner', 'error');
        }
        
        // Get cameras and auto-select
        Html5Qrcode.getCameras().then(devices => {
            if (devices && devices.length) {
                selectedCameraId = selectBestCamera(devices);
                startBtn.disabled = false;
                showResult('Camera ready! Click "Start Auto Camera" to begin.', 'info');
            } else {
                log('No cameras found');
                showResult('No cameras found', 'error');
            }
        }).catch(err => {
            log('Error getting cameras: ' + err);
            showResult('Error accessing cameras', 'error');
        });
        
        startBtn.addEventListener('click', function() {
            if (!selectedCameraId) {
                showResult('No camera selected', 'error');
                return;
            }
            
            log('Starting camera: ' + selectedCameraId);
            startBtn.disabled = true;
            
            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 }
            };
            
            html5QrCode.start(
                selectedCameraId,
                config,
                (decodedText) => {
                    log('QR Code scanned: ' + decodedText);
                    showResult('QR Code: ' + decodedText, 'success');
                },
                (errorMessage) => {
                    // Only log non-routine errors
                    if (!errorMessage.includes('NotFoundException')) {
                        log('Scan error: ' + errorMessage);
                    }
                }
            ).then(() => {
                isScanning = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                log('✓ Camera started successfully');
                showResult('Camera active - scanning for QR codes...', 'success');
                
            }).catch(err => {
                log('✗ Error starting camera: ' + err);
                showResult('Error starting camera: ' + err, 'error');
                startBtn.disabled = false;
            });
        });
        
        stopBtn.addEventListener('click', function() {
            if (html5QrCode && isScanning) {
                log('Stopping camera...');
                stopBtn.disabled = true;
                
                html5QrCode.stop().then(() => {
                    isScanning = false;
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    log('✓ Camera stopped successfully');
                    showResult('Camera stopped', 'info');
                }).catch(err => {
                    log('✗ Error stopping camera: ' + err);
                    showResult('Error stopping camera', 'error');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                });
            }
        });
        
        log('Test page loaded');
    </script>
</body>
</html>
