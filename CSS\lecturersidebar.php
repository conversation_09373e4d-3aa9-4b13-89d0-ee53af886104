 
<style type="text/css">
  /* Modern Sidebar Styles */
  :root {
    --primary-color: #175883;
    --primary-dark: #0d3c60;
    --primary-light: #2980b9;
    --accent-color: #cbb09c;
    --text-light: #ffffff;
    --text-dark: #333333;
    --bg-light: #f8f9fa;
    --bg-dark: #2d3a4b;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
  }

  #menuToggle {
    display: none;
  }

  #menuIcon {
    cursor: pointer;
    display: block;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 10;
    color: var(--primary-color);
    background-color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
  }

  #menuIcon:hover {
    background-color: var(--primary-color);
    color: white;
  }

  #menuToggle:checked ~ #menuIcon {
    left: 210px;
    background-color: var(--accent-color);
  }

  #menuToggle:checked ~ #menu {
    left: 0;
    box-shadow: 2px 0 15px rgba(0,0,0,0.1);
  }

  #menu {
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    height: 100%;
    background: var(--bg-dark);
    transition: all 0.3s ease-in-out;
    z-index: 5;
    padding-top: 70px;
    box-shadow: none;
  }

  #menu .sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 15px;
  }

  #menu .sidebar-header h3 {
    color: var(--text-light);
    margin: 0;
    font-size: 1.5rem;
  }

  #menu a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--text-light);
    transition: all 0.3s;
    border-left: 4px solid transparent;
    font-size: 15px;
  }

  #menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }

  #menu a:hover, #menu a.active {
    background: rgba(255,255,255,0.1);
    border-left: 4px solid var(--accent-color);
    color: var(--accent-color);
  }

  #menu .logout-link {
    position: absolute;
    bottom: 20px;
    width: 100%;
  }

  #menu .logout-link a {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    margin: 0 20px;
    border-radius: 4px;
    text-align: center;
  }

  #menu .logout-link a:hover {
    background-color: #e74c3c;
    color: white;
    border-left: 4px solid #e74c3c;
  }
</style>

<!-- Link to Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<!-- Collapsible Sidebar -->
<input type="checkbox" id="menuToggle">
<label for="menuToggle" id="menuIcon"><i class="fas fa-bars"></i></label>

<div id="menu">
  <div class="sidebar-header">
    <h3>Lecturer Portal</h3>
  </div>
  
  <a href="lecturer_dashboard.php"><i class="fas fa-home"></i> Home</a>
  <a href="scan_venue.php"><i class="fas fa-qrcode"></i> Scan Venue</a>
  <a href="venues.php"><i class="fas fa-building"></i> View Venues</a>
  <a href="venue_history.php"><i class="fas fa-history"></i> Venue History</a>
  <a href="report_issue.php"><i class="fas fa-exclamation-circle"></i> Report Issue</a>
  <a href="notifications.php"><i class="fas fa-bell"></i> Notifications</a>
  <a href="change_password.php"><i class="fas fa-lock"></i> Change Password</a>
  
  <div class="logout-link">
    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
  </div>
</div>

<script>
  // Add active class to current page link
  document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop();
    const menuLinks = document.querySelectorAll('#menu a');
    
    menuLinks.forEach(link => {
      const linkPage = link.getAttribute('href');
      if (linkPage === currentPage) {
        link.classList.add('active');
      }
    });
  });
</script>
