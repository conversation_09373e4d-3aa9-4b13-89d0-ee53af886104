<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit();
}

// Database connection
require_once "database.php";

// Get user ID and role
$user_id = $_SESSION["user"];
$user_role = $_SESSION["role"];

// Add debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the incoming data
$raw_input = file_get_contents('php://input');
error_log("Raw input: " . $raw_input);

// Process checkout request
if (isset($_POST['action']) && $_POST['action'] === 'checkout' && isset($_POST['checkin_id']) && isset($_POST['venue_id'])) {
    $checkin_id = $_POST['checkin_id'];
    $venue_id = $_POST['venue_id'];
    
    // Update the check-in record with checkout time
    $checkout_query = "UPDATE venue_checkins SET check_out_time = NOW() WHERE id = ? AND user_id = ?";
    $checkout_stmt = mysqli_prepare($conn, $checkout_query);
    mysqli_stmt_bind_param($checkout_stmt, "ii", $checkin_id, $user_id);
    
    if (mysqli_stmt_execute($checkout_stmt)) {
        // Check if there are any other active users in this venue
        $active_users_query = "SELECT COUNT(*) as active_count FROM venue_checkins 
                              WHERE venue_id = ? AND check_out_time IS NULL";
        $active_users_stmt = mysqli_prepare($conn, $active_users_query);
        mysqli_stmt_bind_param($active_users_stmt, "i", $venue_id);
        mysqli_stmt_execute($active_users_stmt);
        $active_users_result = mysqli_stmt_get_result($active_users_stmt);
        $active_users = mysqli_fetch_assoc($active_users_result);
        
        // Get venue details
        $venue_query = "SELECT * FROM venues WHERE venueid = ?";
        $venue_stmt = mysqli_prepare($conn, $venue_query);
        mysqli_stmt_bind_param($venue_stmt, "i", $venue_id);
        mysqli_stmt_execute($venue_stmt);
        $venue_result = mysqli_stmt_get_result($venue_stmt);
        $venue = mysqli_fetch_assoc($venue_result);
        
        if ($active_users['active_count'] == 0 && $venue['status'] !== 'maintenance') {
            // Update venue status to available
            $update_venue_query = "UPDATE venues SET status = 'available' WHERE venueid = ?";
            $update_venue_stmt = mysqli_prepare($conn, $update_venue_query);
            mysqli_stmt_bind_param($update_venue_stmt, "i", $venue_id);
            mysqli_stmt_execute($update_venue_stmt);
        }
        
        // Redirect back to dashboard with success message
        $_SESSION['success_message'] = "Successfully checked out from " . $venue['venuename'];
        header("Location: lecturer_dashboard.php");
        exit();
    } else {
        // Redirect back with error
        $_SESSION['error_message'] = "Error checking out: " . mysqli_error($conn);
        header("Location: lecturer_dashboard.php");
        exit();
    }
}

// Check if venue ID or code is provided
if (isset($_POST['venue_id']) || isset($_POST['venue_code'])) {
    
    // Get venue ID either directly or by code
    if (isset($_POST['venue_id'])) {
        $venue_id = $_POST['venue_id'];
        
        // Check if venue exists
        $venue_query = "SELECT * FROM venues WHERE venueid = ?";
        $stmt = mysqli_prepare($conn, $venue_query);
        mysqli_stmt_bind_param($stmt, "i", $venue_id);
    } else {
        $venue_code = $_POST['venue_code'];
        
        // Try to parse JSON if it's in JSON format
        $json_data = json_decode($venue_code, true);
        
        if ($json_data && isset($json_data['venueid'])) {
            // If it's valid JSON with venueid
            $venue_id = $json_data['venueid'];
            
            // Check if venue exists by ID
            $venue_query = "SELECT * FROM venues WHERE venueid = ?";
            $stmt = mysqli_prepare($conn, $venue_query);
            mysqli_stmt_bind_param($stmt, "i", $venue_id);
        } else {
            // Check if it's a venue code or try to extract venue ID from text format
            if (is_numeric($venue_code)) {
                // If it's just a number, treat as venue ID
                $venue_query = "SELECT * FROM venues WHERE venueid = ?";
                $stmt = mysqli_prepare($conn, $venue_query);
                mysqli_stmt_bind_param($stmt, "i", $venue_code);
            } else if (preg_match('/Venue ID: (\d+)/i', $venue_code, $matches)) {
                // Extract venue ID from text format
                $venue_id = $matches[1];
                $venue_query = "SELECT * FROM venues WHERE venueid = ?";
                $stmt = mysqli_prepare($conn, $venue_query);
                mysqli_stmt_bind_param($stmt, "i", $venue_id);
            } else {
                // Check if venue exists by code
                $venue_query = "SELECT * FROM venues WHERE venue_code = ?";
                $stmt = mysqli_prepare($conn, $venue_query);
                mysqli_stmt_bind_param($stmt, "s", $venue_code);
            }
        }
    }
    
    mysqli_stmt_execute($stmt);
    $venue_result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($venue_result) > 0) {
        $venue = mysqli_fetch_assoc($venue_result);
        $venue_id = $venue['venueid'];
        
        // Check if user already has an active check-in for this venue
        $check_query = "SELECT * FROM venue_checkins WHERE user_id = ? AND venue_id = ? AND check_out_time IS NULL";
        $check_stmt = mysqli_prepare($conn, $check_query);
        mysqli_stmt_bind_param($check_stmt, "ii", $user_id, $venue_id);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);
        
        if (mysqli_num_rows($check_result) > 0) {
            // User is already checked in, so check them out
            $checkin = mysqli_fetch_assoc($check_result);
            $checkin_id = $checkin['checkin_id'];
            
            // Update check-out time
            $checkout_query = "UPDATE venue_checkins SET check_out_time = NOW() WHERE checkin_id = ?";
            $checkout_stmt = mysqli_prepare($conn, $checkout_query);
            mysqli_stmt_bind_param($checkout_stmt, "i", $checkin_id);
            
            if (mysqli_stmt_execute($checkout_stmt)) {
                // Check if venue is now empty and update status if needed
                $active_users_query = "SELECT COUNT(*) as active_count FROM venue_checkins WHERE venue_id = ? AND check_out_time IS NULL";
                $active_users_stmt = mysqli_prepare($conn, $active_users_query);
                mysqli_stmt_bind_param($active_users_stmt, "i", $venue_id);
                mysqli_stmt_execute($active_users_stmt);
                $active_users_result = mysqli_stmt_get_result($active_users_stmt);
                $active_users = mysqli_fetch_assoc($active_users_result);
                
                if ($active_users['active_count'] == 0 && $venue['status'] !== 'maintenance') {
                    // Update venue status to available
                    $update_venue_query = "UPDATE venues SET status = 'available' WHERE venueid = ?";
                    $update_venue_stmt = mysqli_prepare($conn, $update_venue_query);
                    mysqli_stmt_bind_param($update_venue_stmt, "i", $venue_id);
                    mysqli_stmt_execute($update_venue_stmt);
                }
                
                // Success response
                $response = [
                    'success' => true,
                    'message' => 'Successfully checked out from ' . $venue['venuename'],
                    'action' => 'checkout',
                    'venue' => $venue['venuename']
                ];
            } else {
                // Error response
                $response = [
                    'success' => false,
                    'message' => 'Error checking out: ' . mysqli_error($conn)
                ];
            }
        } else {
            // Different logic based on user role
            if ($user_role === 'lecturer') {
                // Lecturers can check in to any available venue
                if ($venue['status'] !== 'available' && $venue['status'] !== 'free') {
                    $response = [
                        'success' => false,
                        'message' => 'This venue is currently ' . $venue['status'] . ' and not available for check-in.'
                    ];
                } else {
                    // Check in the lecturer
                    $checkin_query = "INSERT INTO venue_checkins (user_id, venue_id, check_in_time) VALUES (?, ?, NOW())";
                    $checkin_stmt = mysqli_prepare($conn, $checkin_query);
                    mysqli_stmt_bind_param($checkin_stmt, "ii", $user_id, $venue_id);
                    
                    if (mysqli_stmt_execute($checkin_stmt)) {
                        // Update venue status to 'active' (for student attendance)
                        $update_venue_query = "UPDATE venues SET status = 'active' WHERE venueid = ?";
                        $update_venue_stmt = mysqli_prepare($conn, $update_venue_query);
                        mysqli_stmt_bind_param($update_venue_stmt, "i", $venue_id);
                        mysqli_stmt_execute($update_venue_stmt);
                        
                        // Success response
                        $response = [
                            'success' => true,
                            'message' => 'Successfully checked in to ' . $venue['venuename'] . '. Students can now mark attendance.',
                            'action' => 'checkin',
                            'venue' => $venue['venuename']
                        ];
                    } else {
                        // Error response
                        $response = [
                            'success' => false,
                            'message' => 'Error checking in: ' . mysqli_error($conn)
                        ];
                    }
                }
            } else if ($user_role === 'student') {
                // Students can only check in to venues that are 'active' (lecturer is present)
                if ($venue['status'] !== 'active') {
                    $response = [
                        'success' => false,
                        'message' => 'This venue requires a lecturer to check in first before students can mark attendance.'
                    ];
                } else {
                    // Check in the student (attendance)
                    $checkin_query = "INSERT INTO venue_checkins (user_id, venue_id, check_in_time) VALUES (?, ?, NOW())";
                    $checkin_stmt = mysqli_prepare($conn, $checkin_query);
                    mysqli_stmt_bind_param($checkin_stmt, "ii", $user_id, $venue_id);
                    
                    if (mysqli_stmt_execute($checkin_stmt)) {
                        // Success response
                        $response = [
                            'success' => true,
                            'message' => 'Attendance marked for ' . $venue['venuename'],
                            'action' => 'attendance',
                            'venue' => $venue['venuename']
                        ];
                    } else {
                        // Error response
                        $response = [
                            'success' => false,
                            'message' => 'Error marking attendance: ' . mysqli_error($conn)
                        ];
                    }
                }
            } else {
                // Admin or other roles
                $response = [
                    'success' => false,
                    'message' => 'Your role does not allow venue check-ins.'
                ];
            }
        }
    } else {
        // Venue not found
        $response = [
            'success' => false,
            'message' => 'Venue not found. Please check the QR code or venue code and try again.'
        ];
    }
} else {
    // No venue ID or code provided
    $response = [
        'success' => false,
        'message' => 'No venue information provided.'
    ];
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit();


