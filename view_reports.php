<?php
session_start();
require_once "database.php";

// Only admins can access
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    header("Location: login.php");
    exit();
}

// Handle resolve action
if (isset($_GET["resolve"]) && is_numeric($_GET["resolve"])) {
    $reportId = $_GET["resolve"];
    $resolveSQL = "UPDATE reports SET status = 'resolved' WHERE report_id = ?";
    $stmt = mysqli_prepare($conn, $resolveSQL);
    mysqli_stmt_bind_param($stmt, "i", $reportId);
    mysqli_stmt_execute($stmt);
    header("Location: view_reports.php");
    exit();
}

// Fetch all reports with user and venue info
$query = "
    SELECT r.report_id, r.description, r.status, r.reported_at,
           u.full_name, u.email,
           v.venuename
    FROM reports r
    JOIN users u ON r.user_id = u.user_id
    JOIN venues v ON r.venue_id = v.venueid
    ORDER BY r.reported_at DESC
";
$result = mysqli_query($conn, $query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        .container {
            max-width: 1000px;
            margin: 40px auto;
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            font-family: 'Roboto', sans-serif;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            text-align: left;
            padding: 10px;
            border: 1px solid #ddd;
        }

        th {
            background-color: #007BFF;
            color: white;
        }

        .badge {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        .pending { background-color: #ffc107; color: #000; }
        .resolved { background-color: #28a745; color: white; }

        .btn-action {
            background: #17a2b8;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
        }

        .btn-action:hover {
            background-color: #138496;
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="container">
    <h2>Reported Venue Issues</h2>

    <?php if (mysqli_num_rows($result) > 0): ?>
        <table>
            <tr>
                <th>Reported By</th>
                <th>Email</th>
                <th>Venue</th>
                <th>Description</th>
                <th>Status</th>
                <th>Date</th>
                <th>Action</th>
            </tr>
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <tr>
                    <td><?= htmlspecialchars($row["full_name"]) ?></td>
                    <td><?= htmlspecialchars($row["email"]) ?></td>
                    <td><?= htmlspecialchars($row["venuename"]) ?></td>
                    <td><?= htmlspecialchars($row["description"]) ?></td>
                    <td>
                        <span class="badge <?= $row["status"] === "resolved" ? "resolved" : "pending" ?>">
                            <?= ucfirst($row["status"]) ?>
                        </span>
                    </td>
                    <td><?= date("Y-m-d H:i", strtotime($row["reported_at"])) ?></td>
                    <td>
                        <?php if ($row["status"] === "pending"): ?>
                            <a class="btn-action" href="?resolve=<?= $row["report_id"] ?>">Mark Resolved</a>
                        <?php else: ?>
                            —
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endwhile; ?>
        </table>
    <?php else: ?>
        <p>No reports found.</p>
    <?php endif; ?>
</div>

<?php include('CSS/footer.php'); ?>
</body>
</html>
