<?php
// Show errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once "database.php";

// Optional access check
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    header("Location: login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
</head>
<body>
<?php include('CSS/sidebar.php'); ?>

<h2 style="margin-left: 270px; margin-top: 30px;">Upload Timetable CSV</h2>

<div style="margin-left: 270px;">
    <form method="post" enctype="multipart/form-data">
        <input type="file" name="timetable" accept=".csv" required>
        <button type="submit">Upload</button>
    </form>
</div>

<?php
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_FILES["timetable"])) {
    $file = $_FILES["timetable"]["tmp_name"];
    $handle = fopen($file, "r");

    if ($handle !== FALSE) {
        fgetcsv($handle); // skip header

        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $course = mysqli_real_escape_string($conn, $data[0]);
            $year = (int)$data[1];
            $stream = mysqli_real_escape_string($conn, $data[2]);
            $day = mysqli_real_escape_string($conn, $data[3]);
            $start = $data[4];
            $end = $data[5];
            $type = mysqli_real_escape_string($conn, $data[6]);
            $subject = mysqli_real_escape_string($conn, $data[7]);
            $venue = mysqli_real_escape_string($conn, $data[8]);
            $lecturer = mysqli_real_escape_string($conn, $data[9]);

            $sql = "INSERT INTO timetables (course, year, stream, day, start_time, end_time, type, subject_code, venue, lecturer)
                    VALUES ('$course', $year, '$stream', '$day', '$start', '$end', '$type', '$subject', '$venue', '$lecturer')";

            mysqli_query($conn, $sql);
        }

        fclose($handle);
        echo "<p style='color: green; margin-left: 270px;'>✅ Timetable uploaded successfully!</p>";
    } else {
        echo "<p style='color: red; margin-left: 270px;'>❌ Failed to read the file.</p>";
    }
}
?>

<?php include('CSS/footer.php'); ?>
</body>
</html>
