<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Get user details
$user_id = $_SESSION["user"];
$user_role = $_SESSION["role"];

// Get user name for display
$user_query = "SELECT full_name FROM users WHERE user_id = ?";
$stmt = mysqli_prepare($conn, $user_query);
mysqli_stmt_bind_param($stmt, "i", $user_id);
mysqli_stmt_execute($stmt);
$user_result = mysqli_stmt_get_result($stmt);
$user_data = mysqli_fetch_assoc($user_result);
$user_name = $user_data['full_name'] ?? 'User';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Alternative QR Scanner</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 15px;
            transition: margin-left 0.3s ease;
        }
        
        /* Adjust container when sidebar is open */
        #menuToggle:checked ~ .container {
            margin-left: 250px;
        }
        
        @media (max-width: 768px) {
            #menuToggle:checked ~ .container {
                margin-left: 0;
                opacity: 0.4;
            }
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .scanner-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            margin-bottom: 25px;
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            font-size: 18px;
            font-weight: 500;
            text-align: center;
        }
        
        .card-body {
            padding: 30px;
            text-align: center;
        }
        
        #video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #000;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: var(--primary-dark);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .result.success {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .result.error {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger-color);
            border: 1px solid var(--danger-color);
        }
        
        .manual-form {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 20px;
            transition: color 0.3s;
        }
        
        .back-button:hover {
            color: var(--primary-dark);
        }
    </style>
</head>
<body>

<?php 
if ($user_role === 'lecturer') {
    include('CSS/lecturersidebar.php');
} else {
    include('CSS/studentsidebar.php');
}
?>

<div class="container">
    <a href="<?php echo ($user_role === 'lecturer') ? 'lecturer_dashboard.php' : 'student_dashboard.php'; ?>" class="back-button">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
    
    <div class="page-header">
        <h1>Alternative QR Scanner</h1>
        <p>Using native browser camera API</p>
    </div>
    
    <div class="scanner-card">
        <div class="card-header">
            <i class="fas fa-camera"></i> Camera Scanner
        </div>
        <div class="card-body">
            <video id="video" autoplay playsinline></video>
            <canvas id="canvas" style="display: none;"></canvas>
            
            <div class="controls">
                <button id="start-btn" class="btn">
                    <i class="fas fa-play"></i> Start Camera
                </button>
                <button id="stop-btn" class="btn" disabled>
                    <i class="fas fa-stop"></i> Stop Camera
                </button>
                <button id="capture-btn" class="btn" disabled>
                    <i class="fas fa-camera"></i> Capture & Scan
                </button>
            </div>
            
            <div id="result" class="result"></div>
            
            <div class="manual-form">
                <h3>Manual Entry</h3>
                <form action="process_venue_scan.php" method="post">
                    <div class="form-group">
                        <label for="venue_code">Venue Code or ID:</label>
                        <input type="text" id="venue_code" name="venue_code" placeholder="Enter venue code" required>
                    </div>
                    <input type="hidden" name="action" value="checkin">
                    <button type="submit" class="btn">
                        <i class="fas fa-sign-in-alt"></i> Check In
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const captureBtn = document.getElementById('capture-btn');
    const result = document.getElementById('result');
    
    let stream = null;
    let scanning = false;
    
    function showResult(message, type) {
        result.className = `result ${type}`;
        result.innerHTML = message;
        result.style.display = 'block';
    }
    
    function hideResult() {
        result.style.display = 'none';
    }
    
    startBtn.addEventListener('click', async function() {
        try {
            stream = await navigator.mediaDevices.getUserMedia({ 
                video: { 
                    facingMode: 'environment',
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                } 
            });
            
            video.srcObject = stream;
            video.play();
            
            startBtn.disabled = true;
            stopBtn.disabled = false;
            captureBtn.disabled = false;
            
            showResult('Camera started. Use "Capture & Scan" to scan QR codes.', 'success');
            
            // Auto-scan every 2 seconds
            scanning = true;
            autoScan();
            
        } catch (err) {
            console.error('Error accessing camera:', err);
            showResult('Error accessing camera: ' + err.message, 'error');
        }
    });
    
    stopBtn.addEventListener('click', function() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
            stream = null;
        }
        
        video.srcObject = null;
        scanning = false;
        
        startBtn.disabled = false;
        stopBtn.disabled = true;
        captureBtn.disabled = true;
        
        showResult('Camera stopped.', 'success');
    });
    
    captureBtn.addEventListener('click', function() {
        scanFrame();
    });
    
    function autoScan() {
        if (!scanning) return;
        
        scanFrame();
        setTimeout(autoScan, 2000); // Scan every 2 seconds
    }
    
    function scanFrame() {
        if (!video.videoWidth || !video.videoHeight) return;
        
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        ctx.drawImage(video, 0, 0);
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code) {
            console.log('QR Code found:', code.data);
            showResult('QR Code detected: ' + code.data, 'success');
            
            // Process the QR code
            processQrCode(code.data);
        }
    }
    
    function processQrCode(qrData) {
        showResult('Processing QR code...', 'success');
        
        fetch('process_venue_scan.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `venue_code=${encodeURIComponent(qrData)}&action=checkin`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '<?php echo ($user_role === "lecturer") ? "lecturer_dashboard.php" : "student_dashboard.php"; ?>';
                }, 3000);
            } else {
                showResult(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error processing QR code:', error);
            showResult('Error processing QR code. Please try again.', 'error');
        });
    }
});
</script>

</body>
</html>
