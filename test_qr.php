<?php
// Simple test to create a QR code for testing
require_once 'phpqrcode.php';

// Create a simple venue data for testing
$venue_data = json_encode([
    'venueid' => 1,
    'venue_name' => 'Test Venue',
    'timestamp' => time()
]);

// Generate QR code
$filename = 'test_venue_qr.png';
QRcode::png($venue_data, $filename, QR_ECLEVEL_L, 10, 2);

echo "Test QR code generated: $filename<br>";
echo "QR Code data: " . htmlspecialchars($venue_data) . "<br>";
echo "<img src='$filename' alt='Test QR Code'>";
?>
