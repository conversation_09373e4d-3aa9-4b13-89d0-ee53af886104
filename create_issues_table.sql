-- Create issues table
CREATE TABLE IF NOT EXISTS issues (
    issue_id INT AUTO_INCREMENT PRIMARY KEY,
    venue_id INT,
    user_id INT,
    issue_title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    issue_type VARCHAR(50) NOT NULL,
    status ENUM('pending', 'in_progress', 'resolved', 'closed') DEFAULT 'pending',
    reported_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_date DATETIME DEFAULT NULL,
    resolved_by INT DEFAULT NULL,
    resolution_notes TEXT DEFAULT NULL,
    FOREIGN KEY (venue_id) REFERENCES venues(venueid) ON DELETE SET NULL,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;