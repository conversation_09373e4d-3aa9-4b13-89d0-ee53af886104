<?php
session_start();

// Check if user is already logged in
if (isset($_SESSION["user"])) {
    // Redirect based on role
    if ($_SESSION["role"] === "admin") {
        header("Location: index.php");
    } elseif ($_SESSION["role"] === "lecturer") {
        header("Location: lecturer_dashboard.php");
    } elseif ($_SESSION["role"] === "student") {
        header("Location: student_dashboard.php");
    }
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$username = $password = "";
$error_message = "";

// Process login form
if (isset($_POST["login"])) {
    // Get form data
    $username = mysqli_real_escape_string($conn, $_POST["username"]);
    $password = $_POST["password"];

    // Validate form data
    if (empty($username) || empty($password)) {
        $error_message = "All fields are required";
    } else {
        // Check if username exists
        $sql = "SELECT * FROM users WHERE email = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);

        if (mysqli_num_rows($result) === 1) {
            $row = mysqli_fetch_assoc($result);
            
            // Verify password
            if (password_verify($password, $row["password"])) {
                // Set session variables
                $_SESSION["user"] = $row["user_id"];
                $_SESSION["name"] = $row["full_name"];
                $_SESSION["role"] = $row["role"];
                $_SESSION["user_id"] = $row["user_id"];
                
                // Log the successful login
                $ip = $_SERVER['REMOTE_ADDR'];
                $user_agent = $_SERVER['HTTP_USER_AGENT'];
                $log_query = "INSERT INTO login_logs (user_id, ip_address, user_agent) VALUES (?, ?, ?)";
                $log_stmt = mysqli_prepare($conn, $log_query);
                mysqli_stmt_bind_param($log_stmt, "iss", $row["user_id"], $ip, $user_agent);
                mysqli_stmt_execute($log_stmt);
                
                // Redirect based on role
                if ($row["role"] === "admin") {
                    header("Location: index.php");
                } elseif ($row["role"] === "lecturer") {
                    header("Location: lecturer_dashboard.php");
                } elseif ($row["role"] === "student") {
                    header("Location: student_dashboard.php");
                }
                exit();
            } else {
                $error_message = "Invalid username or password";
            }
        } else {
            $error_message = "Invalid username or password";
        }
    }
}

// Close the database connection
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 40px auto;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px 30px;
            text-align: center;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }
        
        .login-header img {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
        }
        
        .login-content {
            padding: 30px;
        }
        
        .login-form {
            max-width: 100%;
            margin: 0;
            padding: 0;
            box-shadow: none;
        }
        
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px 12px 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-icon {
            position: absolute;
            left: 15px;
            top: 40px;
            color: #777;
        }
        
        .btn-login {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-login:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .forgot-password a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        .ifm-header {
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="assets/ifm-logo.png" alt="IFM Logo" onerror="this.src='https://via.placeholder.com/80?text=IFM'">
            <h1>IFM Venue Management System</h1>
        </div>
        
        <div class="login-content">
            <?php if (!empty($error_message)): ?>
                <div class="alert">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form action="login.php" method="post" class="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <i class="fas fa-user form-icon"></i>
                    <input type="text" id="username" name="username" class="form-control" placeholder="Enter your username" value="<?php echo htmlspecialchars($username); ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <i class="fas fa-lock form-icon"></i>
                    <input type="password" id="password" name="password" class="form-control" placeholder="Enter your password">
                </div>
                
                <div class="form-group">
                    <button type="submit" name="login" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </div>
            </form>
        </div>
    </div>
    <?php include('CSS/footer.php'); ?>
</body>
</html>
