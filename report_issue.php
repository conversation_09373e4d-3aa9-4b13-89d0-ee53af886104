<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$success_message = $error_message = "";
$venue_id = isset($_GET['venue']) ? intval($_GET['venue']) : 0;
$selected_venue = null;

// If venue ID is provided, get venue details
if ($venue_id > 0) {
    $venue_query = "SELECT * FROM venues WHERE venueid = ?";
    $stmt = mysqli_prepare($conn, $venue_query);
    mysqli_stmt_bind_param($stmt, "i", $venue_id);
    mysqli_stmt_execute($stmt);
    $venue_result = mysqli_stmt_get_result($stmt);
    
    if ($venue_result && mysqli_num_rows($venue_result) > 0) {
        $selected_venue = mysqli_fetch_assoc($venue_result);
    }
}

// Get all venues for dropdown
$venues_query = "SELECT venueid, venuename, location FROM venues ORDER BY venuename";
$venues_result = mysqli_query($conn, $venues_query);
$venues = [];

if ($venues_result) {
    while ($row = mysqli_fetch_assoc($venues_result)) {
        $venues[] = $row;
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $venue_id = isset($_POST['venue_id']) ? intval($_POST['venue_id']) : 0;
    $issue_title = isset($_POST['issue_title']) ? trim($_POST['issue_title']) : "";
    $description = isset($_POST['description']) ? trim($_POST['description']) : "";
    $issue_type = isset($_POST['issue_type']) ? trim($_POST['issue_type']) : "";
    
    // Validate inputs
    if (empty($venue_id) || empty($issue_title) || empty($description) || empty($issue_type)) {
        $error_message = "All fields are required.";
    } else {
        // Insert the issue into the database
        $insert_query = "INSERT INTO issues (venue_id, user_id, issue_title, description, issue_type, status, reported_date) 
                         VALUES (?, ?, ?, ?, ?, 'pending', NOW())";
        
        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, "iisss", $venue_id, $_SESSION['user'], $issue_title, $description, $issue_type);
        
        if (mysqli_stmt_execute($stmt)) {
            $success_message = "Issue reported successfully. Thank you for your feedback!";
            
            // Clear form data after successful submission
            $venue_id = 0;
            $issue_title = "";
            $description = "";
            $issue_type = "";
            $selected_venue = null;
        } else {
            $error_message = "Failed to report issue. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Report Issue | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
        }
        
        .form-container {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-dark);
            font-weight: 500;
        }
        
        select, input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
        }
        
        textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .selected-venue {
            background-color: rgba(23, 88, 131, 0.1);
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .selected-venue h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .venue-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
            color: var(--text-dark);
        }
        
        .venue-detail i {
            color: var(--primary-color);
            width: 16px;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: var(--primary-dark);
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            .form-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>

<?php 
// Include the appropriate sidebar based on user role
if ($_SESSION["role"] === "admin") {
    include('CSS/sidebar.php');
} elseif ($_SESSION["role"] === "lecturer") {
    include('CSS/lecturersidebar.php');
} elseif ($_SESSION["role"] === "student") {
    include('CSS/studentsidebar.php');
}
?>

<div class="container">
    <h2><i class="fas fa-exclamation-circle"></i> Report Room Issue</h2>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="form-container">
        <?php if ($selected_venue): ?>
            <div class="selected-venue">
                <h3>Selected Venue: <?php echo htmlspecialchars($selected_venue['venuename']); ?></h3>
                <?php if (!empty($selected_venue['location'])): ?>
                    <div class="venue-detail">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Location: <?php echo htmlspecialchars($selected_venue['location']); ?></span>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($selected_venue['capacity'])): ?>
                    <div class="venue-detail">
                        <i class="fas fa-users"></i>
                        <span>Capacity: <?php echo htmlspecialchars($selected_venue['capacity']); ?></span>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($selected_venue['venuedescription'])): ?>
                    <div class="venue-detail">
                        <i class="fas fa-info-circle"></i>
                        <span>Description: <?php echo htmlspecialchars($selected_venue['venuedescription']); ?></span>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <form method="post" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <div class="form-group">
                <label for="venue_id">Venue:</label>
                <select id="venue_id" name="venue_id" required>
                    <option value="">-- Select Venue --</option>
                    <?php foreach ($venues as $venue): ?>
                        <option value="<?php echo $venue['venueid']; ?>" <?php echo ($venue_id == $venue['venueid']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($venue['venuename']); ?> 
                            <?php echo !empty($venue['location']) ? '(' . htmlspecialchars($venue['location']) . ')' : ''; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="issue_type">Issue Type:</label>
                <select id="issue_type" name="issue_type" required>
                    <option value="">-- Select Issue Type --</option>
                    <option value="Equipment" <?php echo ($issue_type === 'Equipment') ? 'selected' : ''; ?>>Equipment Problem</option>
                    <option value="Furniture" <?php echo ($issue_type === 'Furniture') ? 'selected' : ''; ?>>Furniture Problem</option>
                    <option value="Cleanliness" <?php echo ($issue_type === 'Cleanliness') ? 'selected' : ''; ?>>Cleanliness Issue</option>
                    <option value="Electrical" <?php echo ($issue_type === 'Electrical') ? 'selected' : ''; ?>>Electrical Problem</option>
                    <option value="Network" <?php echo ($issue_type === 'Network') ? 'selected' : ''; ?>>Network/Internet Issue</option>
                    <option value="HVAC" <?php echo ($issue_type === 'HVAC') ? 'selected' : ''; ?>>Heating/Cooling Issue</option>
                    <option value="Other" <?php echo ($issue_type === 'Other') ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="issue_title">Issue Title:</label>
                <input type="text" id="issue_title" name="issue_title" value="<?php echo htmlspecialchars($issue_title ?? ''); ?>" required placeholder="Brief title describing the issue">
            </div>
            
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" required placeholder="Please provide details about the issue..."><?php echo htmlspecialchars($description ?? ''); ?></textarea>
            </div>
            
            <button type="submit" class="btn btn-block">
                <i class="fas fa-paper-plane"></i> Submit Report
            </button>
        </form>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var elems = document.querySelectorAll('select');
    M.FormSelect.init(elems);
});
</script>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
        
        // Redirect to same page with venue parameter when venue is changed
        document.getElementById('venue_id').addEventListener('change', function() {
            const venueId = this.value;
            if (venueId) {
                window.location.href = 'report_issue.php?venue=' + venueId;
            }
        });
    });
</script>

</body>
</html>
