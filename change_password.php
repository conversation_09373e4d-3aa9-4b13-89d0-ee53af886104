<?php
session_start();

// Check if the user is logged in
if (!isset($_SESSION["user"])) {
    // Redirect unauthorized users to the login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Initialize variables
$success_message = $error_message = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["change_password"])) {
    $currentPassword = $_POST["current_password"];
    $newPassword = $_POST["new_password"];
    $confirmNewPassword = $_POST["confirm_new_password"];

    // Validate inputs
    if (empty($currentPassword) || empty($newPassword) || empty($confirmNewPassword)) {
        $error_message = "All fields are required";
    } elseif ($newPassword !== $confirmNewPassword) {
        $error_message = "New passwords do not match";
    } elseif (strlen($newPassword) < 8) {
        $error_message = "Password must be at least 8 characters long";
    } else {
        // Get the user's current password from the database
        $userId = $_SESSION["user"];
        $sql = "SELECT password FROM users WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $userId);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if ($row = mysqli_fetch_assoc($result)) {
            // Verify current password
            if (password_verify($currentPassword, $row["password"])) {
                // Hash the new password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                
                // Update the password in the database
                $updateSql = "UPDATE users SET password = ? WHERE id = ?";
                $updateStmt = mysqli_prepare($conn, $updateSql);
                mysqli_stmt_bind_param($updateStmt, "si", $hashedPassword, $userId);
                
                if (mysqli_stmt_execute($updateStmt)) {
                    $success_message = "Password changed successfully";
                } else {
                    $error_message = "Error updating password: " . mysqli_error($conn);
                }
            } else {
                $error_message = "Current password is incorrect";
            }
        } else {
            $error_message = "User not found";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        .password-container {
            max-width: 600px;
            margin: 40px auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .password-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px 30px;
        }
        
        .password-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }
        
        .password-content {
            padding: 30px;
        }
        
        .password-form {
            max-width: 100%;
            margin: 0;
            padding: 0;
            box-shadow: none;
        }
        
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-dark);
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px 12px 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(23, 88, 131, 0.2);
        }
        
        .form-icon {
            position: absolute;
            left: 15px;
            top: 40px;
            color: #777;
        }
        
        .btn-submit {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-submit:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: rgba(46, 204, 113, 0.2);
            border: 1px solid var(--success-color);
            color: #27ae60;
        }
        
        .alert-danger {
            background-color: rgba(231, 76, 60, 0.2);
            border: 1px solid var(--danger-color);
            color: #c0392b;
        }
        
        .password-tips {
            margin-top: 30px;
            padding: 15px;
            background-color: rgba(52, 152, 219, 0.1);
            border-radius: 4px;
        }
        
        .password-tips h3 {
            margin-top: 0;
            color: var(--primary-color);
            font-size: 18px;
        }
        
        .password-tips ul {
            margin-bottom: 0;
            padding-left: 20px;
        }
        
        .password-tips li {
            margin-bottom: 5px;
            color: #555;
        }
    </style>
</head>
<body>

<?php 
// Include the appropriate sidebar based on user role
if ($_SESSION["role"] === "admin") {
    include('CSS/sidebar.php');
} elseif ($_SESSION["role"] === "lecturer") {
    include('CSS/lecturersidebar.php');
} elseif ($_SESSION["role"] === "student") {
    include('CSS/studentsidebar.php');
}
?>

<div class="container">
    <div class="password-container">
        <div class="password-header">
            <h1><i class="fas fa-lock"></i> Change Password</h1>
        </div>
        
        <div class="password-content">
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <form action="change_password.php" method="post" class="password-form">
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <i class="fas fa-key form-icon"></i>
                    <input type="password" id="current_password" name="current_password" class="form-control" placeholder="Enter your current password">
                </div>
                
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <i class="fas fa-lock form-icon"></i>
                    <input type="password" id="new_password" name="new_password" class="form-control" placeholder="Enter your new password">
                </div>
                
                <div class="form-group">
                    <label for="confirm_new_password">Confirm New Password</label>
                    <i class="fas fa-lock form-icon"></i>
                    <input type="password" id="confirm_new_password" name="confirm_new_password" class="form-control" placeholder="Confirm your new password">
                </div>
                
                <div class="form-group">
                    <button type="submit" name="change_password" class="btn-submit">
                        <i class="fas fa-save"></i> Change Password
                    </button>
                </div>
            </form>
            
            <div class="password-tips">
                <h3><i class="fas fa-shield-alt"></i> Password Tips</h3>
                <ul>
                    <li>Use at least 8 characters</li>
                    <li>Include uppercase and lowercase letters</li>
                    <li>Include numbers and special characters</li>
                    <li>Avoid using personal information</li>
                    <li>Don't reuse passwords from other sites</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

</body>
</html>
