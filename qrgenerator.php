<?php
// Check if the QR library is already included
if (!class_exists('QRcode')) {
    // Include the PHP QR Code library only if not already included
    require_once 'phpqrcode.php';
}

// Function to generate QR code and save the image file
function generateQRCode($data, $filename)
{
    // Create a temporary directory for QR code generation
    $tempDir = sys_get_temp_dir();

    // Generate QR code file in temp directory
    $filePath = $tempDir . '/' . $filename . '.png';
    QRcode::png($data, $filePath, QR_ECLEVEL_L, 10, 2);

    // Get the desktop path based on the operating system
    $desktopPath = getDesktopPath();
    
    if (!$desktopPath) {
        throw new Exception("Could not determine desktop path");
    }
    
    // Make sure the desktop directory exists and is writable
    if (!is_dir($desktopPath) || !is_writable($desktopPath)) {
        throw new Exception("Desktop directory doesn't exist or is not writable: " . $desktopPath);
    }
    
    // Full path for the QR code on desktop
    $desktopFilePath = $desktopPath . '/' . $filename . '.png';
    
    // Copy the file to desktop
    if (!copy($filePath, $desktopFilePath)) {
        throw new Exception("Failed to copy QR code to desktop. Check permissions.");
    }
    
    // Delete the temporary QR code file
    unlink($filePath);

    return $desktopFilePath;
}

// Function to get desktop path based on operating system
function getDesktopPath()
{
    // For Windows
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Try multiple methods to find the desktop
        
        // Method 1: Using environment variables
        $desktopPath = getenv('USERPROFILE') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
        
        // Method 2: Using HOME environment variable
        $desktopPath = getenv('HOMEDRIVE') . getenv('HOMEPATH') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
        
        // Method 3: Using the current user's profile
        $username = get_current_user();
        $desktopPath = 'C:' . DIRECTORY_SEPARATOR . 'Users' . DIRECTORY_SEPARATOR . $username . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    } 
    // For Linux
    else if (strtoupper(substr(PHP_OS, 0, 5)) === 'LINUX') {
        $desktopPath = getenv('HOME') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    } 
    // For macOS
    else if (strtoupper(substr(PHP_OS, 0, 6)) === 'DARWIN') {
        $desktopPath = getenv('HOME') . DIRECTORY_SEPARATOR . 'Desktop';
        if (is_dir($desktopPath)) {
            return $desktopPath;
        }
    }
    
    // If we couldn't determine the desktop path
    return false;
}
?>
