<?php
session_start();
require_once "database.php";

$errors = [];
$success = "";

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $name = trim($_POST["name"]);
    $email = trim($_POST["email"]);
    $password = $_POST["password"];
    $course = $_POST["course"] ?? '';
    $year = $_POST["year"] ?? '';
    $stream = $_POST["stream"] ?? '';

    if (!in_array($course, ['BSc IT', 'ODIT'])) {
    $errors[] = "Invalid course selection.";
}
    // Validation
    if (empty($name) || empty($email) || empty($password) || empty($course) || empty($year) || empty($stream)) {
        $errors[] = "All fields are required.";
    }

    // Check if email already exists
    $checkEmail = mysqli_query($conn, "SELECT * FROM users WHERE email = '$email'");
    if (mysqli_num_rows($checkEmail) > 0) {
        $errors[] = "Email is already registered.";
    }

    if (empty($errors)) {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (full_name, email, password, course, year, stream, role)
                VALUES (?, ?, ?, ?, ?, ?, 'student')";

        $stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "ssssss", $name, $email, $hashedPassword, $course, $year, $stream);

        if (mysqli_stmt_execute($stmt)) {
            $success = "✅ Registration successful! <a href='login.php'>Click here to login</a>.";
        } else {
            $errors[] = "Something went wrong. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        .container {
            max-width: 500px;
            margin: 40px auto;
            padding: 25px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            font-family: 'Roboto', sans-serif;
        }

        input, select {
            width: 100%;
            padding: 10px;
            margin: 8px 0 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        button {
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            font-weight: bold;
        }

        button:hover {
            background: #218838;
        }

        .message {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 5px;
        }

        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>

<div class="container">
    <h2>Student Registration</h2>

    <!-- Show messages -->
    <?php foreach ($errors as $e): ?>
        <div class="message error"><?= $e ?></div>
    <?php endforeach; ?>

    <?php if (!empty($success)): ?>
        <div class="message success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST" action="">
        <label>Full Name:</label>
        <input type="text" name="name" required>

        <label>Email:</label>
        <input type="email" name="email" required>

        <label>Password:</label>
        <input type="password" name="password" required>

        <label>Course:</label>
        <select name="course" required>
            <option value="">-- Select Course --</option>
            <option value="BSc IT">BSc IT</option>
            <option value="ODIT">ODIT</option>
        </select>

        <label>Year:</label>
        <select name="year" required>
            <option value="">-- Select Year --</option>
            <option value="1">Year 1</option>
            <option value="2">Year 2</option>
            <option value="3">Year 3</option>
        </select>

        <label>Stream:</label>
        <select name="stream" required>
            <option value="">-- Select Stream --</option>
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
            <option value="SysDev">SysDev</option>
            <option value="SysAdmin">SysAdmin</option>
        </select>

        <button type="submit">Register</button>
    </form>
</div>

<?php include('CSS/footer.php'); ?>


<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var elems = document.querySelectorAll('select');
    M.FormSelect.init(elems);
});
</script>

</body>
</html>
