<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple QR Video Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            text-align: center;
        }
        
        .container {
            border: 2px solid #ddd;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
        }
        
        #qr-reader {
            width: 100%;
            max-width: 400px;
            height: 400px;
            border: 2px solid #175883;
            border-radius: 8px;
            margin: 20px auto;
            background: #000;
        }
        
        /* Force video to be visible */
        #qr-reader video {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
            border-radius: 6px !important;
        }
        
        #qr-reader > div {
            width: 100% !important;
            height: 100% !important;
        }
        
        button {
            padding: 15px 30px;
            margin: 10px;
            font-size: 16px;
            background: #175883;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #bbdefb;
        }
        
        .debug {
            background: #f5f5f5;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Simple QR Video Feed Test</h1>
    
    <div class="container">
        <button id="start-btn">Start Camera</button>
        <button id="stop-btn" disabled>Stop Camera</button>
        
        <div id="qr-reader"></div>
        
        <div id="status" class="status">
            Ready to start camera
        </div>
        
        <div id="debug" class="debug">
            <strong>Debug Log:</strong><br>
        </div>
    </div>

    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <script>
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const status = document.getElementById('status');
        const debug = document.getElementById('debug');
        const qrReader = document.getElementById('qr-reader');
        
        let html5QrCode;
        let isScanning = false;
        let selectedCameraId = null;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debug.innerHTML += `<br>[${timestamp}] ${message}`;
            debug.scrollTop = debug.scrollHeight;
            console.log(message);
        }
        
        function setStatus(message) {
            status.innerHTML = message;
            log(`Status: ${message}`);
        }
        
        function selectBestCamera(devices) {
            log(`Found ${devices.length} cameras:`);
            devices.forEach((device, index) => {
                log(`  ${index + 1}. ${device.label || 'Unknown'} (${device.id.substring(0, 20)}...)`);
            });
            
            // Prefer back camera
            const backCamera = devices.find(device => 
                device.label.toLowerCase().includes('back') || 
                device.label.toLowerCase().includes('environment') ||
                device.label.toLowerCase().includes('rear')
            );
            
            if (backCamera) {
                log(`✓ Selected back camera: ${backCamera.label}`);
                return backCamera.id;
            }
            
            log(`✓ Selected first camera: ${devices[0].label || 'Unknown'}`);
            return devices[0].id;
        }
        
        // Initialize
        try {
            html5QrCode = new Html5Qrcode("qr-reader");
            log('✓ QR scanner initialized');
            setStatus('QR scanner initialized, getting cameras...');
        } catch (error) {
            log('✗ Error initializing QR scanner: ' + error);
            setStatus('Error initializing scanner');
        }
        
        // Get cameras
        Html5Qrcode.getCameras().then(devices => {
            if (devices && devices.length) {
                selectedCameraId = selectBestCamera(devices);
                startBtn.disabled = false;
                setStatus(`Camera ready! Found ${devices.length} camera(s). Click Start to begin.`);
            } else {
                log('✗ No cameras found');
                setStatus('No cameras found');
            }
        }).catch(err => {
            log('✗ Error getting cameras: ' + err);
            setStatus('Error accessing cameras: ' + err);
        });
        
        startBtn.addEventListener('click', function() {
            if (!selectedCameraId) {
                setStatus('No camera available');
                return;
            }
            
            log('Starting camera...');
            setStatus('Starting camera...');
            startBtn.disabled = true;
            
            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0
            };
            
            html5QrCode.start(
                selectedCameraId,
                config,
                (decodedText) => {
                    log('✓ QR Code scanned: ' + decodedText);
                    setStatus('QR Code detected: ' + decodedText);
                },
                (errorMessage) => {
                    // Only log non-routine errors
                    if (!errorMessage.includes('NotFoundException')) {
                        log('Scan error: ' + errorMessage);
                    }
                }
            ).then(() => {
                isScanning = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                log('✓ Camera started successfully');
                setStatus('Camera active - you should see video feed above');
                
                // Check video element after a delay
                setTimeout(() => {
                    const videoElement = qrReader.querySelector('video');
                    if (videoElement) {
                        log(`✓ Video element found: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
                        log(`Video style: ${videoElement.style.cssText}`);
                        log(`Video display: ${getComputedStyle(videoElement).display}`);
                        
                        // Force video to be visible
                        videoElement.style.display = 'block';
                        videoElement.style.width = '100%';
                        videoElement.style.height = '100%';
                        videoElement.style.objectFit = 'cover';
                        
                    } else {
                        log('✗ No video element found');
                        // Check what's in the qr-reader
                        log('QR Reader contents: ' + qrReader.innerHTML.substring(0, 200));
                    }
                }, 2000);
                
            }).catch(err => {
                log('✗ Error starting camera: ' + err);
                setStatus('Error starting camera: ' + err);
                startBtn.disabled = false;
            });
        });
        
        stopBtn.addEventListener('click', function() {
            if (html5QrCode && isScanning) {
                log('Stopping camera...');
                setStatus('Stopping camera...');
                stopBtn.disabled = true;
                
                html5QrCode.stop().then(() => {
                    isScanning = false;
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    log('✓ Camera stopped');
                    setStatus('Camera stopped');
                }).catch(err => {
                    log('✗ Error stopping camera: ' + err);
                    setStatus('Error stopping camera');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                });
            }
        });
        
        log('Page loaded and ready');
    </script>
</body>
</html>
