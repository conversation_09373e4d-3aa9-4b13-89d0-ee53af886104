// Add this after successful login in login.php
if (password_verify($password, $row["password"])) {
    // Set session variables
    $_SESSION["user"] = $row["user_id"];
    $_SESSION["name"] = $row["full_name"];
    $_SESSION["role"] = $row["role"];
    $_SESSION["user_id"] = $row["user_id"];
    
    // Log the successful login
    $ip = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    $log_query = "INSERT INTO login_logs (user_id, ip_address, user_agent) VALUES (?, ?, ?)";
    $log_stmt = mysqli_prepare($conn, $log_query);
    mysqli_stmt_bind_param($log_stmt, "iss", $row["user_id"], $ip, $user_agent);
    mysqli_stmt_execute($log_stmt);
    
    // Redirect based on role
    if ($row["role"] === "admin") {
        header("Location: index.php");
    } elseif ($row["role"] === "lecturer") {
        header("Location: lecturer_dashboard.php");
    } elseif ($row["role"] === "student") {
        header("Location: student_dashboard.php");
    }
    exit();
}