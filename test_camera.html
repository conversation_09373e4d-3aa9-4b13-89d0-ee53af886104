<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        
        #qr-reader {
            width: 100%;
            max-width: 400px;
            height: 400px;
            border: 2px solid #ddd;
            margin: 20px auto;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        select, button {
            padding: 10px;
            margin: 5px;
            font-size: 16px;
        }
        
        .result {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>QR Scanner Camera Test</h1>
    
    <div class="controls">
        <select id="camera-select">
            <option value="">Loading cameras...</option>
        </select>
        <br>
        <button id="start-btn">Start Camera</button>
        <button id="stop-btn" disabled>Stop Camera</button>
    </div>
    
    <div id="qr-reader"></div>
    
    <div id="result" class="result"></div>
    
    <div id="debug-log" style="background: #f8f9fa; padding: 15px; margin-top: 20px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
        <strong>Debug Log:</strong><br>
    </div>

    <script src="https://unpkg.com/html5-qrcode"></script>
    <script>
        const cameraSelect = document.getElementById('camera-select');
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const result = document.getElementById('result');
        const debugLog = document.getElementById('debug-log');
        
        let html5QrCode;
        let isScanning = false;
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `<br>[${timestamp}] ${message}`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        function showResult(message, type) {
            result.className = `result ${type}`;
            result.innerHTML = message;
            result.style.display = 'block';
            log(`Result: ${message}`);
        }
        
        // Initialize
        try {
            html5QrCode = new Html5Qrcode("qr-reader");
            log('QR scanner initialized successfully');
        } catch (error) {
            log('Error initializing QR scanner: ' + error);
            showResult('Error initializing scanner', 'error');
        }
        
        // Get cameras
        Html5Qrcode.getCameras().then(devices => {
            log(`Found ${devices.length} cameras`);
            cameraSelect.innerHTML = '<option value="">Select Camera...</option>';
            
            devices.forEach((device, index) => {
                const option = document.createElement('option');
                option.value = device.id;
                option.text = device.label || `Camera ${index + 1}`;
                cameraSelect.appendChild(option);
                log(`Camera ${index + 1}: ${device.label || 'Unknown'} (${device.id})`);
            });
            
            startBtn.disabled = false;
        }).catch(err => {
            log('Error getting cameras: ' + err);
            showResult('Error accessing cameras', 'error');
        });
        
        startBtn.addEventListener('click', function() {
            const cameraId = cameraSelect.value;
            if (!cameraId) {
                showResult('Please select a camera', 'error');
                return;
            }
            
            log('Starting camera: ' + cameraId);
            startBtn.disabled = true;
            
            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 }
            };
            
            html5QrCode.start(
                cameraId,
                config,
                (decodedText) => {
                    log('QR Code scanned: ' + decodedText);
                    showResult('QR Code: ' + decodedText, 'success');
                },
                (errorMessage) => {
                    // Only log non-routine errors
                    if (!errorMessage.includes('NotFoundException')) {
                        log('Scan error: ' + errorMessage);
                    }
                }
            ).then(() => {
                isScanning = true;
                startBtn.disabled = true;
                stopBtn.disabled = false;
                log('Camera started successfully');
                showResult('Camera active - point at QR code', 'info');
                
                // Monitor camera state
                const monitor = setInterval(() => {
                    if (!isScanning) {
                        clearInterval(monitor);
                        return;
                    }
                    log('Camera still running...');
                }, 5000);
                
            }).catch(err => {
                log('Error starting camera: ' + err);
                showResult('Error starting camera: ' + err, 'error');
                startBtn.disabled = false;
            });
        });
        
        stopBtn.addEventListener('click', function() {
            if (html5QrCode && isScanning) {
                log('Stopping camera...');
                stopBtn.disabled = true;
                
                html5QrCode.stop().then(() => {
                    isScanning = false;
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    log('Camera stopped successfully');
                    showResult('Camera stopped', 'info');
                }).catch(err => {
                    log('Error stopping camera: ' + err);
                    showResult('Error stopping camera', 'error');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                });
            }
        });
        
        // Page visibility handling
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                log('Page hidden');
            } else {
                log('Page visible');
            }
        });
        
        log('Test page loaded');
    </script>
</body>
</html>
