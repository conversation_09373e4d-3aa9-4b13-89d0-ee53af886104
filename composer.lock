{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5e056bf27f43f96d8267edaaa579424e", "packages": [{"name": "bacon/bacon-qr-code", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "448ee9929aece0e86f0e2b926e636f9b53d03ce1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/448ee9929aece0e86f0e2b926e636f9b53d03ce1", "reference": "448ee9929aece0e86f0e2b926e636f9b53d03ce1", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "suggest": {"ext-gd": "to generate QR code images"}, "type": "library", "autoload": {"psr-0": {"BaconQrCode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/master"}, "time": "2016-11-26T13:57:10+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}