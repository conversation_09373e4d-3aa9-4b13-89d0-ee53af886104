<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitbb7f58007f85901d85fc9818544cafec
{
    public static $prefixesPsr0 = array (
        'B' => 
        array (
            'BaconQrCode' => 
            array (
                0 => __DIR__ . '/..' . '/bacon/bacon-qr-code/src',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixesPsr0 = ComposerStaticInitbb7f58007f85901d85fc9818544cafec::$prefixesPsr0;
            $loader->classMap = ComposerStaticInitbb7f58007f85901d85fc9818544cafec::$classMap;

        }, null, ClassLoader::class);
    }
}
