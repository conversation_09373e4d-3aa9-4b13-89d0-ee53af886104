<?php
session_start();

// Check if the user is logged in and is not an admin
if (isset($_SESSION["user"]) && $_SESSION["role"] !== "admin") {
    // Redirect non-admin users to their respective dashboards
    if ($_SESSION["role"] === "student") {
        header("Location: student_dashboard.php");
    } elseif ($_SESSION["role"] === "lecturer") {
        header("Location: lecturer_dashboard.php");
    } else {
        header("Location: index.php");
    }
    exit();
}

// Database connection
require_once "database.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $fullName = $_POST["fullname"];
    $email = $_POST["email"];

    // Validate inputs
    $errors = array();
    if (empty($fullName) || empty($email)) {
        array_push($errors, "All fields are required");
    }

    if (empty($errors)) {
        // Check if the user with the given name and email exists
        $sql = "SELECT * FROM users WHERE full_name = ? AND email = ?";
        $stmt = mysqli_stmt_init($conn);
        if (!mysqli_stmt_prepare($stmt, $sql)) {
            die("SQL error");
        } else {
            mysqli_stmt_bind_param($stmt, "ss", $fullName, $email);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $rowCount = mysqli_num_rows($result);

            if ($rowCount > 0) {
                // Send a request to the admin
                $insertSql = "INSERT INTO password_reset_requests (full_name, email) VALUES (?, ?)";
                $insertStmt = mysqli_stmt_init($conn);
                if (!mysqli_stmt_prepare($insertStmt, $insertSql)) {
                    die("SQL error");
                } else {
                    mysqli_stmt_bind_param($insertStmt, "ss", $fullName, $email);
                    mysqli_stmt_execute($insertStmt);
                    echo "<div class='alert alert-success'>Password reset request sent successfully. Wait for admin approval.</div>";
                }
            } else {
                array_push($errors, "Invalid name or email");
            }
        }
    }

    if (!empty($errors)) {
        foreach ($errors as $error) {
            echo "<div class='alert alert-danger'>$error</div>";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Include your CSS and other header content -->
    <?php include('CSS/header.php'); ?>
</head>
<body>

<div>
    <!-- Forgot Password form -->
    <form action="forgot_password.php" method="post">
        <div class="form-group">
            <input type="text" class="input-field" name="fullname" placeholder="Full Name">
        </div>
        <div class="form-group">
            <input type="email" class="input-field" name="email" placeholder="Email">
        </div>
        <div class="form-btn">
            <input type="submit" class="text-center px-4 py-2 rounded-md font-semibold text-sm text-white uppercase tracking-widest focus:outline-none focus:border-gray-900 focus:shadow-outline-gray disabled:opacity-25 transition mt-4 w-full" style="background-color: #175883" value="Submit">
            <p><a href = login.php>Login</a>
        </div>
    </form>
</div>

<?php include('CSS/footer.php'); ?>

</body>
</html>
