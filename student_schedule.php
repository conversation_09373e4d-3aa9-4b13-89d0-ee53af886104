<?php
session_start();
require_once "database.php";

// Redirect if not a logged-in student
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "student") {
    header("Location: login.php");
    exit();
}

// Default values from session
$course = isset($_SESSION['course']) ? $_SESSION['course'] : '';
$year = isset($_SESSION['year']) ? $_SESSION['year'] : '';
$stream = isset($_SESSION['stream']) ? $_SESSION['stream'] : '';

// If form is submitted, override the session values
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $course = $_POST['course'] ?? $course;
    $year = $_POST['year'] ?? $year;
    $stream = $_POST['stream'] ?? $stream;
}

// Always fetch timetable (either using session or form data)
$query = "SELECT * FROM timetables 
          WHERE course = ? AND year = ? AND stream = ?
          ORDER BY FIELD(day, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday'), start_time";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, "sis", $course, $year, $stream);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Class Schedule | Student Dashboard</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 15px;
        }
        
        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 24px;
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }
        
        form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-dark);
            font-weight: 500;
        }
        
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: var(--primary-dark);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        th, td {
            text-align: left;
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        tr:nth-child(even) {
            background-color: rgba(0,0,0,0.02);
        }
        
        tr:hover {
            background-color: rgba(23, 88, 131, 0.05);
        }
        
        p {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: var(--text-dark);
            text-align: center;
        }
        
        @media (max-width: 768px) {
            form {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 0 10px;
                margin: 20px auto;
            }
            
            table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/studentsidebar.php'); ?>

<div class="container">
    <h2><i class="fas fa-calendar-alt"></i> Your Class Timetable</h2>

    <form method="post">
        <div>
            <label>Course:</label>
            <select name="course" required>
                <option value="">-- Select Course --</option>
                <option value="BSc IT" <?= $course === 'BSc IT' ? 'selected' : '' ?>>BSc IT</option>
                <option value="ODIT" <?= $course === 'ODIT' ? 'selected' : '' ?>>ODIT</option>
            </select>
        </div>

        <div>
            <label>Year:</label>
            <select name="year" required>
                <option value="">-- Select Year --</option>
                <?php for ($i = 1; $i <= 4; $i++): ?>
                    <option value="<?= $i ?>" <?= $year == $i ? 'selected' : '' ?>><?= $i ?></option>
                <?php endfor; ?>
            </select>
        </div>

        <div>
            <label>Stream:</label>
            <select name="stream" required>
                <option value="">-- Select Stream --</option>
                <option value="A" <?= $stream === 'A' ? 'selected' : '' ?>>A</option>
                <option value="B" <?= $stream === 'B' ? 'selected' : '' ?>>B</option>
                <option value="C" <?= $stream === 'C' ? 'selected' : '' ?>>C</option>
                <option value="SysDev" <?= $stream === 'SysDev' ? 'selected' : '' ?>>SysDev</option>
                <option value="SysAdmin" <?= $stream === 'SysAdmin' ? 'selected' : '' ?>>SysAdmin</option>
            </select>
        </div>

        <div>
            <button type="submit"><i class="fas fa-search"></i> View Schedule</button>
        </div>
    </form>

    <?php if ($result && mysqli_num_rows($result) > 0): ?>
        <table>
            <tr>
                <th>Day</th>
                <th>Start</th>
                <th>End</th>
                <th>Subject</th>
                <th>Type</th>
                <th>Venue</th>
                <th>Lecturer</th>
            </tr>
            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                <tr>
                    <td><?= htmlspecialchars($row['day']) ?></td>
                    <td><?= htmlspecialchars($row['start_time']) ?></td>
                    <td><?= htmlspecialchars($row['end_time']) ?></td>
                    <td><?= htmlspecialchars($row['subject_code']) ?></td>
                    <td><?= htmlspecialchars($row['type']) ?></td>
                    <td><?= htmlspecialchars($row['venue']) ?></td>
                    <td><?= htmlspecialchars($row['lecturer']) ?></td>
                </tr>
            <?php endwhile; ?>
        </table>
    <?php else: ?>
        <p><i class="fas fa-info-circle"></i> No timetable found for the selected filters.</p>
    <?php endif; ?>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');
        
        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>
