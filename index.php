<?php
session_start();

// Check if the user is logged in and is an admin
if (!isset($_SESSION["user"]) || $_SESSION["role"] !== "admin") {
    // Redirect unauthorized users to login page
    header("Location: login.php");
    exit();
}

// Database connection
require_once "database.php";

// Fetch total number of users
$sqlUsers = "SELECT COUNT(*) AS total_users FROM users";
$resultUsers = mysqli_query($conn, $sqlUsers);
$rowUsers = mysqli_fetch_assoc($resultUsers);
$totalUsers = $rowUsers['total_users'];

// Fetch total number of venues
$sqlVenues = "SELECT COUNT(*) AS total_venues FROM venues";
$resultVenues = mysqli_query($conn, $sqlVenues);
$rowVenues = mysqli_fetch_assoc($resultVenues);
$totalVenues = $rowVenues['total_venues'];

// Fetch total number of free venues
$sqlFreeVenues = "SELECT COUNT(*) AS total_free_venues FROM venues WHERE status = 'free'";
$resultFreeVenues = mysqli_query($conn, $sqlFreeVenues);
$rowFreeVenues = mysqli_fetch_assoc($resultFreeVenues);
$totalFreeVenues = $rowFreeVenues['total_free_venues'];

// Fetch recent issues reported
$sqlIssues = "SELECT i.*, v.venuename FROM issues i 
              JOIN venues v ON i.venue_id = v.venueid 
              ORDER BY i.reported_date DESC LIMIT 5";
$resultIssues = mysqli_query($conn, $sqlIssues);

// Fetch recent user activities
$sqlActivities = "SELECT u.full_name, u.role, l.login_time 
                 FROM login_logs l 
                 JOIN users u ON l.user_id = u.user_id 
                 ORDER BY l.login_time DESC LIMIT 5";
$resultActivities = mysqli_query($conn, $sqlActivities);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <title>Admin Dashboard | Venue Management System</title>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
        }
        
        .dashboard-container {
            padding: 30px;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.05);
            margin: 20px;
            transition: margin-left 0.3s ease;
        }
        
        #menuToggle:checked ~ .dashboard-container {
            margin-left: 270px;
        }
        
        .welcome-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .welcome-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 150px;
            height: 100%;
            background: url('images/pattern.svg') no-repeat right center;
            opacity: 0.1;
        }
        
        .welcome-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 500;
        }
        
        .welcome-header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.12);
        }

        .stat-card .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 15px;
        }

        .stat-card .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-card.users .stat-icon {
            background: var(--info-color);
        }

        .stat-card.venues .stat-icon {
            background: var(--success-color);
        }

        .stat-card.free-venues .stat-icon {
            background: var(--warning-color);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .content-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .content-card-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-card-header i {
            margin-right: 10px;
        }

        .content-card-body {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .issue-item, .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .issue-item:last-child, .activity-item:last-child {
            border-bottom: none;
        }

        .issue-details, .activity-details {
            flex: 1;
        }

        .issue-title, .activity-name {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .issue-venue, .activity-role {
            font-size: 14px;
            color: #666;
        }

        .issue-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .issue-status.pending {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning-color);
        }

        .issue-status.resolved {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success-color);
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }

            #menuToggle:checked ~ .dashboard-container {
                margin-left: 0;
                opacity: 0.4;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/sidebar.php'); ?>

<div class="dashboard-container">
    <div class="welcome-header">
        <h1>Welcome to Admin Dashboard</h1>
        <p>Manage your venue system efficiently with comprehensive tools and insights</p>
    </div>

    <div class="stats-container">
        <div class="stat-card users">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number"><?php echo $totalUsers; ?></div>
            <div class="stat-label">Total Users</div>
        </div>

        <div class="stat-card venues">
            <div class="stat-icon">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-number"><?php echo $totalVenues; ?></div>
            <div class="stat-label">Total Venues</div>
        </div>

        <div class="stat-card free-venues">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-number"><?php echo $totalFreeVenues; ?></div>
            <div class="stat-label">Available Venues</div>
        </div>
    </div>

    <div class="content-grid">
        <div class="content-card">
            <div class="content-card-header">
                <span><i class="fas fa-exclamation-triangle"></i> Recent Issues</span>
                <a href="view_reports.php" style="color: white; text-decoration: none; font-size: 14px;">View All</a>
            </div>
            <div class="content-card-body">
                <?php if (mysqli_num_rows($resultIssues) > 0): ?>
                    <?php while ($issue = mysqli_fetch_assoc($resultIssues)): ?>
                        <div class="issue-item">
                            <div class="issue-details">
                                <div class="issue-title"><?php echo htmlspecialchars($issue['issue_description']); ?></div>
                                <div class="issue-venue">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo htmlspecialchars($issue['venuename']); ?>
                                </div>
                            </div>
                            <div class="issue-status <?php echo strtolower($issue['status']); ?>">
                                <?php echo htmlspecialchars($issue['status']); ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-clipboard-check"></i>
                        <p>No recent issues reported</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="content-card">
            <div class="content-card-header">
                <span><i class="fas fa-clock"></i> Recent Activities</span>
                <a href="login_logs.php" style="color: white; text-decoration: none; font-size: 14px;">View All</a>
            </div>
            <div class="content-card-body">
                <?php if (mysqli_num_rows($resultActivities) > 0): ?>
                    <?php while ($activity = mysqli_fetch_assoc($resultActivities)): ?>
                        <div class="activity-item">
                            <div class="activity-details">
                                <div class="activity-name"><?php echo htmlspecialchars($activity['full_name']); ?></div>
                                <div class="activity-role">
                                    <i class="fas fa-user"></i>
                                    <?php echo ucfirst(htmlspecialchars($activity['role'])); ?>
                                </div>
                            </div>
                            <div class="activity-time">
                                <?php echo date('M d, g:i A', strtotime($activity['login_time'])); ?>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-user-clock"></i>
                        <p>No recent activities</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Add active class to current page link
    document.addEventListener('DOMContentLoaded', function() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('#menu a');

        menuLinks.forEach(link => {
            const linkPage = link.getAttribute('href');
            if (linkPage === currentPage || (currentPage === 'index.php' && linkPage === 'index.php')) {
                link.classList.add('active');
            }
        });
    });
</script>

</body>
</html>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          