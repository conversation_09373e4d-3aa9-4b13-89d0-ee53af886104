<?php
require_once "database.php";

/**
 * Notification System for Smart Reminders
 * 
 * This file contains functions to:
 * 1. Check for lecturers who haven't checked out of venues
 * 2. Send reminders for upcoming classes
 * 3. Create and manage notifications
 */

/**
 * Create a new notification for a user
 * 
 * @param int $user_id The user ID to send the notification to
 * @param string $title The notification title
 * @param string $message The notification message
 * @param string $type The notification type (info, warning, danger, success)
 * @param string $action_url Optional URL for action button
 * @return bool True if notification was created successfully
 */
function createNotification($user_id, $title, $message, $type = 'info', $action_url = '') {
    global $conn;
    
    $query = "INSERT INTO notifications (user_id, title, message, type, action_url, created_at) 
              VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($conn, $query);
    
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "issss", $user_id, $title, $message, $type, $action_url);
        return mysqli_stmt_execute($stmt);
    }
    
    return false;
}

/**
 * Check for lecturers who haven't checked out of venues
 * and send them reminder notifications
 */
function checkForMissingCheckouts() {
    global $conn;
    
    // Find check-ins without check-outs that are older than 2 hours
    $query = "SELECT c.id, c.user_id, c.venue_id, c.check_in_time, v.venuename, u.name
              FROM venue_checkins c
              JOIN venues v ON c.venue_id = v.venueid
              JOIN users u ON c.user_id = u.id
              WHERE c.check_out_time IS NULL 
              AND c.check_in_time < DATE_SUB(NOW(), INTERVAL 2 HOUR)";
    
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Check if we already sent a notification for this check-in
            $notificationQuery = "SELECT id FROM notifications 
                                 WHERE user_id = ? 
                                 AND title LIKE 'Forgot to check out%'
                                 AND message LIKE ?
                                 AND created_at > DATE_SUB(NOW(), INTERVAL 4 HOUR)";
            $stmt = mysqli_prepare($conn, $notificationQuery);
            
            $messageLike = "%{$row['venuename']}%";
            mysqli_stmt_bind_param($stmt, "is", $row['user_id'], $messageLike);
            mysqli_stmt_execute($stmt);
            $notificationResult = mysqli_stmt_get_result($stmt);
            
            // Only send notification if we haven't sent one recently
            if (mysqli_num_rows($notificationResult) == 0) {
                $title = "Forgot to check out of venue";
                $message = "You checked into {$row['venuename']} at " . 
                           date('h:i A', strtotime($row['check_in_time'])) . 
                           " but haven't checked out yet. Please remember to check out when you leave.";
                
                createNotification(
                    $row['user_id'],
                    $title,
                    $message,
                    'warning',
                    'venue_history.php'
                );
            }
        }
    }
}

/**
 * Send reminders for upcoming classes
 */
function sendUpcomingClassReminders() {
    global $conn;
    
    // Get current day of week
    $dayOfWeek = date('l'); // Monday, Tuesday, etc.
    
    // Get current time
    $currentTime = date('H:i:s');
    
    // Find classes starting in the next 30 minutes
    $query = "SELECT t.*, u.id as user_id, u.name
              FROM timetables t
              JOIN users u ON t.lecturer LIKE CONCAT('%', u.name, '%')
              WHERE t.day = ?
              AND t.start_time BETWEEN TIME(DATE_ADD(NOW(), INTERVAL 15 MINUTE)) 
                               AND TIME(DATE_ADD(NOW(), INTERVAL 30 MINUTE))";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $dayOfWeek);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Check if we already sent a notification for this class today
            $notificationQuery = "SELECT id FROM notifications 
                                 WHERE user_id = ? 
                                 AND title LIKE 'Upcoming class reminder%'
                                 AND message LIKE ?
                                 AND created_at > CURDATE()";
            $stmt = mysqli_prepare($conn, $notificationQuery);
            
            $messageLike = "%{$row['subject_code']}%{$row['venue']}%";
            mysqli_stmt_bind_param($stmt, "is", $row['user_id'], $messageLike);
            mysqli_stmt_execute($stmt);
            $notificationResult = mysqli_stmt_get_result($stmt);
            
            // Only send notification if we haven't sent one today
            if (mysqli_num_rows($notificationResult) == 0) {
                $startTime = date('h:i A', strtotime($row['start_time']));
                
                $title = "Upcoming class reminder";
                $message = "You have a {$row['type']} class for {$row['subject_code']} at {$startTime} in {$row['venue']}. " .
                           "Course: {$row['course']} Year {$row['year']} ({$row['stream']})";
                
                createNotification(
                    $row['user_id'],
                    $title,
                    $message,
                    'info',
                    'lecturer_schedule.php'
                );
            }
        }
    }
}

/**
 * Check for nearby free venues when a user is viewing the free venues page
 * 
 * @param int $user_id The user ID
 * @param string $current_building The building the user is currently in
 * @return array Array of nearby free venues
 */
function getNearbyFreeVenues($current_building) {
    global $conn;
    
    // Get all free venues
    $query = "SELECT * FROM venues WHERE status = 'free' ORDER BY 
              CASE WHEN building = ? THEN 0 ELSE 1 END, 
              venuename ASC";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $current_building);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $venues = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $venues[] = $row;
    }
    
    return $venues;
}

// Run these functions when this file is included in a cron job
if (isset($argv[0]) && basename($argv[0]) == basename(__FILE__)) {
    checkForMissingCheckouts();
    sendUpcomingClassReminders();
    echo "Notification checks completed at " . date('Y-m-d H:i:s') . "\n";
}
?>