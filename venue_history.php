<?php
session_start();
require_once "database.php";

// Check if user is logged in
if (!isset($_SESSION["user"])) {
    header("Location: login.php");
    exit();
}

// Get user information
$user_id = $_SESSION["user_id"];
$user_role = $_SESSION["role"];

// Only lecturers can view venue history
if ($user_role !== 'lecturer') {
    header("Location: index.php");
    exit();
}

// Build query with filters
$query = "SELECT c.*, v.venuename 
          FROM venue_checkins c
          JOIN venues v ON c.venue_id = v.venueid
          WHERE c.user_id = ?";

$params = [$user_id];
$types = "i";

// Apply venue filter if set
if (isset($_GET['venue']) && !empty($_GET['venue'])) {
    $query .= " AND c.venue_id = ?";
    $params[] = $_GET['venue'];
    $types .= "i";
}

// Apply date range filters if set
if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    $query .= " AND DATE(c.check_in_time) >= ?";
    $params[] = $_GET['date_from'];
    $types .= "s";
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    $query .= " AND DATE(c.check_in_time) <= ?";
    $params[] = $_GET['date_to'];
    $types .= "s";
}

// Order by check-in time, most recent first
$query .= " ORDER BY c.check_in_time DESC";

// Prepare and execute the query
$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, $types, ...$params);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <?php include('CSS/header.php'); ?>
    <style>
        :root {
            --primary-color: #175883;
            --primary-dark: #0d3c60;
            --primary-light: #2980b9;
            --accent-color: #cbb09c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f8f9fa;
            --bg-dark: #2d3a4b;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
        }
        
        body {
            background-color: var(--bg-light);
            font-family: 'Roboto', sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0 15px;
        }
        
        .page-header {
            margin-bottom: 30px;
        }
        
        .page-header h2 {
            color: var(--primary-color);
            margin: 0 0 10px 0;
            font-size: 28px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .page-header p {
            color: #666;
            margin: 0;
        }
        
        .history-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        
        .history-header {
            background-color: var(--primary-color);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .history-filters {
            padding: 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-size: 14px;
            color: #555;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            min-width: 150px;
        }
        
        .filter-button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 9px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.3s;
        }
        
        .filter-button:hover {
            background-color: var(--primary-dark);
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .history-table th,
        .history-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .history-table th {
            background-color: #f8f9fa;
            font-weight: 500;
            color: #555;
        }
        
        .history-table tr:last-child td {
            border-bottom: none;
        }
        
        .venue-name {
            font-weight: 500;
            color: var(--primary-color);
        }
        
        .check-time {
            color: #555;
        }
        
        .duration {
            font-weight: 500;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-complete {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        
        .status-ongoing {
            background-color: #fff8e1;
            color: #f57f17;
        }
        
        .empty-state {
            padding: 50px 20px;
            text-align: center;
            color: #777;
        }
        
        .empty-state i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .empty-state p {
            margin: 5px 0;
            font-size: 16px;
        }
        
        @media (max-width: 768px) {
            .filter-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .history-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>

<?php include('CSS/lecturersidebar.php'); ?>

<div class="container">
    <div class="page-header">
        <h2><i class="fas fa-history"></i> Venue Usage History</h2>
        <p>View your past venue check-ins and check-outs</p>
    </div>
    
    <div class="history-card">
        <div class="history-header">
            <i class="fas fa-list"></i> History Records
        </div>
        
        <div class="history-filters">
            <form method="GET" action="venue_history.php" class="filter-form">
                <div class="filter-group">
                    <label for="venue">Venue:</label>
                    <select name="venue" id="venue">
                        <option value="">All Venues</option>
                        <?php
                        // Get all venues used by this lecturer
                        $venueQuery = "SELECT DISTINCT v.venuename, v.venueid 
                                      FROM venues v 
                                      JOIN venue_checkins c ON v.venueid = c.venue_id 
                                      WHERE c.user_id = ?
                                      ORDER BY v.venuename";
                        $venueStmt = mysqli_prepare($conn, $venueQuery);
                        mysqli_stmt_bind_param($venueStmt, "i", $user_id);
                        mysqli_stmt_execute($venueStmt);
                        $venueResult = mysqli_stmt_get_result($venueStmt);
                        
                        $selectedVenue = isset($_GET['venue']) ? $_GET['venue'] : '';
                        
                        while ($venue = mysqli_fetch_assoc($venueResult)) {
                            $selected = ($selectedVenue == $venue['venueid']) ? 'selected' : '';
                            echo "<option value='{$venue['venueid']}' $selected>{$venue['venuename']}</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="date_from">From:</label>
                    <input type="date" name="date_from" id="date_from" value="<?php echo isset($_GET['date_from']) ? $_GET['date_from'] : ''; ?>">
                </div>
                
                <div class="filter-group">
                    <label for="date_to">To:</label>
                    <input type="date" name="date_to" id="date_to" value="<?php echo isset($_GET['date_to']) ? $_GET['date_to'] : ''; ?>">
                </div>
                
                <button type="submit" class="filter-button">
                    <i class="fas fa-filter"></i> Filter
                </button>
                
                <button type="button" class="filter-button" onclick="window.location.href='venue_history.php'">
                    <i class="fas fa-sync-alt"></i> Reset
                </button>
            </form>
        </div>
        
        <?php if (mysqli_num_rows($result) > 0): ?>
            <table class="history-table">
                <thead>
                    <tr>
                        <th>Venue</th>
                        <th>Check-in Time</th>
                        <th>Check-out Time</th>
                        <th>Duration</th>
                        <th>Purpose</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = mysqli_fetch_assoc($result)): ?>
                        <?php
                        // Calculate duration
                        $checkInTime = strtotime($row['check_in_time']);
                        $checkOutTime = !empty($row['check_out_time']) ? strtotime($row['check_out_time']) : time();
                        $duration = $checkOutTime - $checkInTime;
                        
                        // Format duration
                        $hours = floor($duration / 3600);
                        $minutes = floor(($duration % 3600) / 60);
                        $durationFormatted = ($hours > 0 ? $hours . 'h ' : '') . $minutes . 'm';
                        
                        // Determine status
                        $status = !empty($row['check_out_time']) ? 'complete' : 'ongoing';
                        ?>
                        <tr>
                            <td class="venue-name"><?php echo htmlspecialchars($row['venuename']); ?></td>
                            <td class="check-time">
                                <?php echo date('M d, Y - h:i A', strtotime($row['check_in_time'])); ?>
                            </td>
                            <td class="check-time">
                                <?php 
                                if (!empty($row['check_out_time'])) {
                                    echo date('M d, Y - h:i A', strtotime($row['check_out_time']));
                                } else {
                                    echo '<span style="color: var(--warning-color);">Not checked out</span>';
                                }
                                ?>
                            </td>
                            <td class="duration"><?php echo $durationFormatted; ?></td>
                            <td><?php echo htmlspecialchars($row['purpose'] ?? 'N/A'); ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $status; ?>">
                                    <?php echo $status === 'complete' ? 'Completed' : 'Ongoing'; ?>
                                </span>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <p>No venue usage history found.</p>
                <p>When you check in to venues, your history will appear here.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include('CSS/footer.php'); ?>

<script>
    // JavaScript for enhancing the filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Set default date values if not already set
        const dateFrom = document.getElementById('date_from');
        const dateTo = document.getElementById('date_to');
        
        if (!dateFrom.value) {
            // Default to 30 days ago
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            dateFrom.value = thirtyDaysAgo.toISOString().split('T')[0];
        }
        
        if (!dateTo.value) {
            // Default to today
            const today = new Date();
            dateTo.value = today.toISOString().split('T')[0];
        }
    });
</script>

</body>
</html>

