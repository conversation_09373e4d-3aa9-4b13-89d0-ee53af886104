{"packages": [{"name": "bacon/bacon-qr-code", "version": "1.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "448ee9929aece0e86f0e2b926e636f9b53d03ce1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/448ee9929aece0e86f0e2b926e636f9b53d03ce1", "reference": "448ee9929aece0e86f0e2b926e636f9b53d03ce1", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "suggest": {"ext-gd": "to generate QR code images"}, "time": "2016-11-26T13:57:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"BaconQrCode": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "http://www.dasprids.de", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/master"}, "install-path": "../bacon/bacon-qr-code"}], "dev": true, "dev-package-names": []}