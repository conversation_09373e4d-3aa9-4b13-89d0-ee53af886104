# PowerShell script to import project.sql database
Write-Host "Importing updated project.sql database..." -ForegroundColor Green
Write-Host "This will create/update the database with all required tables:" -ForegroundColor Yellow
Write-Host "- users, venues, timetables, reports, password_reset_requests" -ForegroundColor Cyan
Write-Host "- issues, notifications, login_logs, venue_checkins" -ForegroundColor Cyan
Write-Host ""
Write-Host "Please enter your MySQL root password when prompted." -ForegroundColor Yellow
Write-Host ""

# Change to DB directory and import the SQL file
Set-Location -Path "DB"
& "C:\xampp\mysql\bin\mysql.exe" -u root -p < "project.sql"

Write-Host ""
Write-Host "Database import completed successfully!" -ForegroundColor Green
Write-Host "All required tables have been created/updated." -ForegroundColor Green
Write-Host "You can now access your application at http://localhost/Mine" -ForegroundColor Cyan
Read-Host "Press Enter to continue..."
